/**
 * Patient Custom Field Value Converters
 *
 * Handles value conversion between AutoPatient and CliniCore custom field formats.
 * Provides intelligent type-aware conversion with proper error handling and validation.
 *
 * @fileoverview Value conversion utilities for patient custom field synchronization
 * @version 1.0.0
 * @since 2024-07-29
 */

import { convertFromTextboxListValue } from "@/processors/customFields/config/textboxListHandling";
import type { GetCCCustomField } from "@/type";
import { logWarn, logInfo, logDebug } from "@/utils/logger";
import type {
	APCustomFieldValue,
	CCCustomFieldValue,
	FieldMapping,
	ValueConversionResult,
} from "./types";

/**
 * Universal allowedValues validation and ID mapping for CC custom fields
 *
 * @param values - Array of string values to validate and map
 * @param ccConfig - CC field configuration with allowedValues
 * @returns Array of CC value objects with IDs or raw values
 */
function validateAndMapAllowedValues(
	values: string[],
	ccConfig: GetCCCustomField,
): Array<{ id?: number; value?: string }> {
	const result: Array<{ id?: number; value?: string }> = [];
	const warnings: string[] = [];

	// If no allowedValues defined, use raw values
	if (!ccConfig.allowedValues || ccConfig.allowedValues.length === 0) {
		logDebug("No allowedValues defined for CC field, using raw values", {
			ccFieldName: ccConfig.name,
			ccFieldType: ccConfig.type,
			valuesCount: values.length,
		});
		return values.map(value => ({ value }));
	}

	logDebug("Validating values against allowedValues", {
		ccFieldName: ccConfig.name,
		ccFieldType: ccConfig.type,
		inputValues: values,
		allowedValuesCount: ccConfig.allowedValues.length,
		allowedValues: ccConfig.allowedValues.map(av => av.value),
	});

	for (const value of values) {
		// Try to find matching allowed value
		const allowedValue = ccConfig.allowedValues.find(
			(av) => av.value === value,
		);

		if (allowedValue) {
			// Use predefined option ID
			result.push({ id: allowedValue.id });
			logDebug("Value mapped to allowedValue ID", {
				ccFieldName: ccConfig.name,
				originalValue: value,
				mappedToId: allowedValue.id,
			});
		} else {
			// No match found - use raw value (may cause validation error)
			result.push({ value });
			warnings.push(`Value "${value}" not found in allowed values for field "${ccConfig.name}"`);
			logWarn("Value not found in allowedValues, using raw value", {
				ccFieldName: ccConfig.name,
				ccFieldType: ccConfig.type,
				unmatchedValue: value,
				allowedValues: ccConfig.allowedValues.map(av => av.value),
			});
		}
	}

	if (warnings.length > 0) {
		logWarn("Some values could not be mapped to allowedValue IDs", {
			ccFieldName: ccConfig.name,
			ccFieldType: ccConfig.type,
			totalValues: values.length,
			unmappedCount: warnings.length,
			warnings,
		});
	} else {
		logInfo("All values successfully mapped to allowedValue IDs", {
			ccFieldName: ccConfig.name,
			ccFieldType: ccConfig.type,
			totalValues: values.length,
		});
	}

	return result;
}

/**
 * Convert AutoPatient custom field value to CliniCore format
 *
 * @param apValue - AP custom field value
 * @param mapping - Field mapping from database
 * @returns Converted CC custom field value
 */
export function convertApValueToCc(
	apValue: APCustomFieldValue,
	mapping: FieldMapping,
): ValueConversionResult {
	try {
		const { apConfig, ccConfig } = mapping;
		const sourceValue = apValue.value;

		// Handle null/undefined values
		if (
			sourceValue === null ||
			sourceValue === undefined ||
			sourceValue === ""
		) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValue,
			};
		}

		const ccValue: CCCustomFieldValue = {
			field: ccConfig,
			values: [],
			patient: null,
		};

		// Special handling for AP TEXTBOX_LIST → CC multi-value fields (all compatible types)
		const isTextboxListToMultiValue =
			apConfig.dataType === "TEXTBOX_LIST" &&
			ccConfig.allowMultipleValues &&
			["text", "textarea", "email", "telephone", "number"].includes(
				ccConfig.type,
			);

		if (isTextboxListToMultiValue) {
			// Use the existing textboxListHandling function for consistent conversion
			const convertedValues = convertFromTextboxListValue(
				sourceValue,
				apConfig,
				ccConfig,
			);

			// convertFromTextboxListValue returns string[] for multi-value fields
			if (Array.isArray(convertedValues)) {
				ccValue.values = convertedValues.map((value) => ({ value }));
			} else {
				ccValue.values = [{ value: convertedValues }];
			}

			return {
				success: true,
				convertedValue: ccValue,
				originalValue: sourceValue,
			};
		}

		// Convert based on CC field type for non-TEXTBOX_LIST cases
		switch (ccConfig.type) {
			case "text":
			case "textarea":
			case "email":
			case "telephone": {
				let values: string[];

				// Handle Record<string, string> objects (like TEXTBOX_LIST values) for multi-value CC fields
				if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue) && ccConfig.allowMultipleValues) {
					// Extract values from Record<string, string> and create multiple CC values
					values = Object.values(sourceValue as Record<string, string>)
						.filter((v) => v && typeof v === "string" && v.trim().length > 0)
						.map((v) => v.trim());
				} else {
					values = [String(sourceValue)];
				}

				// Apply universal allowedValues validation and ID mapping
				ccValue.values = validateAndMapAllowedValues(values, ccConfig);
				break;
			}

			case "number": {
				let values: string[];

				// Handle Record<string, string> objects for multi-value number fields
				if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue) && ccConfig.allowMultipleValues) {
					values = Object.values(sourceValue as Record<string, string>)
						.filter((v) => v && typeof v === "string" && v.trim().length > 0)
						.map((v) => v.trim());
				} else {
					// Validate single number value
					const numValue = Number(sourceValue);
					if (Number.isNaN(numValue)) {
						return {
							success: false,
							convertedValue: null,
							originalValue: sourceValue,
							error: `Invalid number value: ${sourceValue}`,
						};
					}
					values = [String(numValue)];
				}

				// Apply universal allowedValues validation and ID mapping
				ccValue.values = validateAndMapAllowedValues(values, ccConfig);
				break;
			}

			case "date": {
				let values: string[];

				// Handle Record<string, string> objects for multi-value date fields
				if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue) && ccConfig.allowMultipleValues) {
					const objectValues = Object.values(sourceValue as Record<string, string>)
						.filter((v) => v && typeof v === "string" && v.trim().length > 0)
						.map((v) => v.trim());
					values = objectValues.map(v => convertToDateString(v)).filter(Boolean) as string[];

					// Check if any date conversion failed
					if (values.length !== objectValues.length) {
						const failedValues = objectValues.filter(v => !convertToDateString(v));
						return {
							success: false,
							convertedValue: null,
							originalValue: sourceValue,
							error: `Invalid date values: ${failedValues.join(', ')}`,
						};
					}
				} else {
					// Date fields don't support arrays, use first value if array
					const dateInput = Array.isArray(sourceValue)
						? sourceValue[0]
						: sourceValue;
					const dateValue = convertToDateString(dateInput);
					if (!dateValue) {
						return {
							success: false,
							convertedValue: null,
							originalValue: sourceValue,
							error: `Invalid date value: ${sourceValue}`,
						};
					}
					values = [dateValue];
				}

				// Apply universal allowedValues validation and ID mapping
				ccValue.values = validateAndMapAllowedValues(values, ccConfig);
				break;
			}

			case "boolean": {
				let values: string[];

				// Handle Record<string, string> objects for multi-value boolean fields (rare but possible)
				if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue) && ccConfig.allowMultipleValues) {
					const objectValues = Object.values(sourceValue as Record<string, string>)
						.filter((v) => v && typeof v === "string" && v.trim().length > 0)
						.map((v) => v.trim());
					values = objectValues.map(v => convertToBooleanString(v));
				} else {
					// Boolean fields don't support arrays, use first value if array
					const boolInput = Array.isArray(sourceValue)
						? sourceValue[0]
						: sourceValue;
					const boolValue = convertToBooleanString(boolInput);
					values = [boolValue];
				}

				// Apply universal allowedValues validation and ID mapping
				ccValue.values = validateAndMapAllowedValues(values, ccConfig);
				break;
			}

			case "select":
			case "select-or-custom": {
				// Handle multi-value select fields vs single-value select fields
				if (ccConfig.allowMultipleValues) {
					// Multi-value select field - handle Record<string, string> and arrays
					logWarn("Processing multi-value select field conversion", {
						ccFieldName: ccConfig.name,
						ccFieldType: ccConfig.type,
						sourceValueType: typeof sourceValue,
						isObject: typeof sourceValue === "object" && sourceValue !== null,
						allowedValuesCount: ccConfig.allowedValues?.length || 0,
					});
					return convertApSelectValueToCc(sourceValue, ccConfig, ccValue);
				} else {
					// Single-value select field - use first value if array or object
					const selectInput = Array.isArray(sourceValue)
						? sourceValue[0]
						: typeof sourceValue === "object" && sourceValue !== null
						? Object.values(sourceValue as Record<string, string>)[0] || ""
						: sourceValue;
					return convertApSelectValueToCc(selectInput, ccConfig, ccValue);
				}
			}

			case "medication":
			case "permanent-diagnoses":
			case "patient-has-recommended": {
				// Medical field types - treat as text fields with allowedValues support
				let values: string[];

				// Handle Record<string, string> objects for multi-value medical fields
				if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue) && ccConfig.allowMultipleValues) {
					values = Object.values(sourceValue as Record<string, string>)
						.filter((v) => v && typeof v === "string" && v.trim().length > 0)
						.map((v) => v.trim());
				} else {
					values = [String(sourceValue)];
				}

				// Apply universal allowedValues validation and ID mapping
				ccValue.values = validateAndMapAllowedValues(values, ccConfig);
				break;
			}

			default: {
				logWarn("Unknown CC field type, using text conversion", {
					ccFieldType: ccConfig.type,
					ccFieldId: ccConfig.id,
					sourceValueType: typeof sourceValue,
					sourceValue: sourceValue,
				});

				let values: string[];

				// Handle Record<string, string> objects for unknown field types with multi-value support
				if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue) && ccConfig.allowMultipleValues) {
					values = Object.values(sourceValue as Record<string, string>)
						.filter((v) => v && typeof v === "string" && v.trim().length > 0)
						.map((v) => v.trim());
				} else {
					values = [String(sourceValue)];
				}

				// Apply universal allowedValues validation and ID mapping
				ccValue.values = validateAndMapAllowedValues(values, ccConfig);
				break;
			}
		}

		return {
			success: true,
			convertedValue: ccValue,
			originalValue: sourceValue,
		};
	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			originalValue: apValue.value,
			error: `Conversion error: ${String(error)}`,
		};
	}
}

/**
 * Convert CliniCore custom field value to AutoPatient format
 *
 * @param ccValue - CC custom field value
 * @param mapping - Field mapping from database
 * @returns Converted AP custom field value
 */
export function convertCcValueToAp(
	ccValue: CCCustomFieldValue,
	mapping: FieldMapping,
): ValueConversionResult {
	try {
		const { apConfig, ccConfig } = mapping;
		const sourceValues = ccValue.values;

		// Handle empty values
		if (!sourceValues || sourceValues.length === 0) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValues,
			};
		}

		const apValue: APCustomFieldValue = {
			id: apConfig.id,
			value: null,
		};

		// Special handling for CC multi-value fields → AP TEXTBOX_LIST
		const isCcMultiValueToTextboxList =
			ccConfig.allowMultipleValues &&
			apConfig.dataType === "TEXTBOX_LIST" &&
			["text", "textarea", "email", "telephone", "number", "date"].includes(
				ccConfig.type,
			);

		if (isCcMultiValueToTextboxList) {
			const values = sourceValues
				.map((v) => v.value)
				.filter(Boolean) as string[];

			// Mark this field for special TEXTBOX_LIST processing
			// The actual field_value mapping will be done by the async processor
			apValue.value = `__TEXTBOX_LIST_VALUES__:${JSON.stringify(values)}`;

			return {
				success: true,
				convertedValue: apValue,
				originalValue: sourceValues,
			};
		}

		// Convert based on AP field type
		switch (apConfig.dataType) {
			case "TEXT":
			case "LARGE_TEXT":
			case "EMAIL":
			case "PHONE":
			case "MONETORY":
				apValue.value = sourceValues[0]?.value || "";
				break;

			case "NUMERICAL": {
				const numStr = sourceValues[0]?.value || "0";
				const numValue = Number(numStr);
				if (Number.isNaN(numValue)) {
					return {
						success: false,
						convertedValue: null,
						originalValue: sourceValues,
						error: `Invalid number value: ${numStr}`,
					};
				}
				apValue.value = numValue;
				break;
			}

			case "DATE": {
				const dateStr = sourceValues[0]?.value || "";
				apValue.value = dateStr;
				break;
			}

			case "RADIO":
			case "SINGLE_OPTIONS":
				apValue.value = sourceValues[0]?.value || "";
				break;

			case "CHECKBOX":
			case "MULTIPLE_OPTIONS":
			case "TEXTBOX_LIST": {
				// Handle multiple values
				const values = sourceValues.map((v) => v.value).filter(Boolean);
				apValue.value = values.join(", ");
				break;
			}

			default:
				logWarn("Unknown AP field type, using text conversion", {
					apFieldType: apConfig.dataType,
					apFieldId: apConfig.id,
				});
				apValue.value = sourceValues[0]?.value || "";
		}

		return {
			success: true,
			convertedValue: apValue,
			originalValue: sourceValues,
		};
	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			originalValue: ccValue.values,
			error: `Conversion error: ${String(error)}`,
		};
	}
}

/**
 * Convert AP select value to CC format with allowed values handling
 */
function convertApSelectValueToCc(
	sourceValue: string | number | boolean | null | Record<string, string>,
	ccConfig: GetCCCustomField,
	ccValue: CCCustomFieldValue,
): ValueConversionResult {
	let values: string[];

	// Handle different input types
	if (typeof sourceValue === "object" && sourceValue !== null && !Array.isArray(sourceValue)) {
		// Handle Record<string, string> objects (from TEXTBOX_LIST fields)
		values = Object.values(sourceValue as Record<string, string>)
			.filter((v) => v && typeof v === "string" && v.trim().length > 0)
			.map((v) => v.trim());

		logWarn("Converting TEXTBOX_LIST object to CC select field", {
			ccFieldName: ccConfig.name,
			ccFieldType: ccConfig.type,
			allowMultipleValues: ccConfig.allowMultipleValues,
			sourceObject: sourceValue,
			extractedValues: values,
			allowedValuesCount: ccConfig.allowedValues?.length || 0,
		});
	} else {
		// Handle primitive values (string, number, boolean, null)
		const valueStr = String(sourceValue || "");

		// Handle multiple values (comma-separated)
		values = valueStr
			.split(",")
			.map((v) => v.trim())
			.filter(Boolean);
	}

	for (const value of values) {
		// Try to find matching allowed value
		const allowedValue = ccConfig.allowedValues?.find(
			(av) => av.value === value,
		);

		if (allowedValue) {
			// Use predefined option ID
			ccValue.values.push({ id: allowedValue.id });
		} else if (ccConfig.type === "select-or-custom") {
			// Allow custom value for select-or-custom fields
			ccValue.values.push({ value });
		} else {
			// For strict select fields, use the value as-is (may cause validation error)
			ccValue.values.push({ value });
		}
	}

	return {
		success: true,
		convertedValue: ccValue,
		originalValue: sourceValue,
		warnings:
			ccConfig.allowedValues?.length > 0 &&
			!ccConfig.allowedValues.some((av) => values.includes(av.value))
				? [
						`Values "${values.join(', ')}" not found in allowed values for field "${ccConfig.name}"`,
					]
				: undefined,
	};
}

/**
 * Convert value to date string format
 */
function convertToDateString(
	value: string | number | boolean | null,
): string | null {
	if (!value) return null;

	try {
		const date = new Date(String(value));
		if (Number.isNaN(date.getTime())) return null;

		// Return ISO date string (YYYY-MM-DD)
		return date.toISOString().split("T")[0];
	} catch {
		return null;
	}
}



/**
 * Convert value to boolean string representation
 */
function convertToBooleanString(
	value: string | number | boolean | null,
): string {
	if (typeof value === "boolean") {
		return value ? "true" : "false";
	}

	const str = String(value).toLowerCase().trim();

	// Handle common boolean representations
	if (["true", "1", "yes", "ja", "on", "enabled"].includes(str)) {
		return "true";
	}

	if (["false", "0", "no", "nein", "off", "disabled"].includes(str)) {
		return "false";
	}

	// Default to false for unknown values
	return "false";
}
