/**
 * Webhook Queue Manager
 *
 * Core class for managing webhook processing queue with status tracking,
 * duplicate prevention, timeout detection, and comprehensive logging.
 * Designed for high-throughput webhook processing within Cloudflare Workers
 * 30-second runtime constraints.
 *
 * @fileoverview Webhook queue management system
 * @version 1.0.0
 * @since 2025-08-06
 */

import { dbSchema, getDb } from "@database";
import { and, eq, inArray, isNull, lt, lte, or } from "drizzle-orm";
import type {
	APContactCreationWebhookPayload,
	WebhookProcessingConfig as APWebhookProcessingConfig,
} from "@/processors/apWebhook/types";
import type { WebhookProcessingConfig } from "@/processors/ccWebhook/types";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { fireAndForgetCustomFieldSync } from "@/utils/fireAndForget";
import { DuplicatePreventionLockManager } from "./DuplicatePreventionLockManager";
import type {
	Alert<PERSON>everity,
	AlertType,
	APWebhookQueueItem,
	BatchProcessingResult,
	CCWebhookQueueItem,
	DuplicateDetectionResult,
	QueueProcessingOptions,
	QueueStatistics,
	TimeoutDetectionResult,
	WebhookProcessingResult,
	WebhookQueueInsert,
	WebhookQueueItem,
	WebhookQueueLogItem,
	WebhookQueueLogStatus,
} from "./types";
import { isAPWebhookQueueItem } from "./types";

/**
 * Helper function to convert null to undefined for optional fields
 * Database returns null but TypeScript types expect undefined
 */
function nullToUndefined<T>(value: T | null): T | undefined {
	return value === null ? undefined : value;
}

/**
 * Helper function to convert database webhook item to typed interface
 */
function convertDbWebhookToTyped(
	dbWebhook: typeof dbSchema.webhookQueue.$inferSelect,
): WebhookQueueItem {
	const baseWebhook = {
		...dbWebhook,
		patientId: nullToUndefined(dbWebhook.patientId),
		appointmentId: nullToUndefined(dbWebhook.appointmentId),
		startedAt: nullToUndefined(dbWebhook.startedAt),
		completedAt: nullToUndefined(dbWebhook.completedAt),
		nextRetryAt: nullToUndefined(dbWebhook.nextRetryAt),
		errorMessage: nullToUndefined(dbWebhook.errorMessage),
		processingTimeMs: nullToUndefined(dbWebhook.processingTimeMs),
		duplicateOf: nullToUndefined(dbWebhook.duplicateOf),
		duplicateReason: nullToUndefined(dbWebhook.duplicateReason),
	};

	// Type assertion based on source field to satisfy discriminated union
	if (dbWebhook.source === "cc") {
		return baseWebhook as CCWebhookQueueItem;
	} else if (dbWebhook.source === "ap") {
		return baseWebhook as APWebhookQueueItem;
	} else {
		throw new Error(`Unknown webhook source: ${dbWebhook.source}`);
	}
}

/**
 * WebhookQueueManager
 *
 * Manages the complete lifecycle of webhook processing including:
 * - Queue insertion with duplicate detection
 * - Status tracking and updates
 * - Timeout detection and handling
 * - Retry logic with exponential backoff
 * - Log management and archiving
 * - Alert generation for monitoring
 *
 * **Key Features:**
 * - Cross-platform duplicate prevention (AP ↔ CC)
 * - 29-second timeout detection based on processing start time
 * - Priority-based queue processing (fresh = 100, retry = 50)
 * - Comprehensive audit trail in logs table
 * - Automatic cleanup and archiving
 * - Real-time monitoring and alerting
 */
export class WebhookQueueManager {
	private db = getDb();
	private lockManager = new DuplicatePreventionLockManager();

	/**
	 * Add webhook to queue with duplicate detection
	 *
	 * Performs comprehensive duplicate detection across platforms and
	 * adds webhook to queue if not duplicate. Handles priority assignment
	 * and initial status setting.
	 *
	 * @param webhook - Webhook data to add to queue
	 * @returns Processing result with webhook ID and status
	 */
	async addWebhook(
		webhook: WebhookQueueInsert,
	): Promise<WebhookProcessingResult> {
		try {
			logDebug("Adding webhook to queue", {
				source: webhook.source,
				entityType: webhook.entityType,
				entityId: webhook.entityId,
			});

			// Check for duplicates
			const duplicateResult = await this.checkForDuplicates(webhook);
			if (duplicateResult.isDuplicate) {
				// Create duplicate log entry immediately
				const webhookId = crypto.randomUUID();
				await this.moveToLogs({
					id: webhookId,
					createdAt: new Date(),
					updatedAt: new Date(),
					status: "duplicate_skipped",
					priority: webhook.priority || 100,
					source: webhook.source,
					entityType: webhook.entityType,
					entityId: webhook.entityId,
					patientId: webhook.patientId,
					appointmentId: webhook.appointmentId,
					payload: webhook.payload,
					retryCount: 0,
					duplicateOf: duplicateResult.originalWebhookId,
					duplicateReason: duplicateResult.reason,
					completedAt: new Date(),
				});

				return {
					webhookId,
					status: "duplicate_skipped",
					duplicateOf: duplicateResult.originalWebhookId,
					duplicateReason: duplicateResult.reason,
				};
			}

			// Insert webhook into queue
			const webhookId = await this.insertWebhook(webhook);

			logInfo("Webhook added to queue successfully", {
				webhookId,
				source: webhook.source,
				entityType: webhook.entityType,
			});

			return {
				webhookId,
				status: "pending",
			};
		} catch (error) {
			logError("Failed to add webhook to queue", error);
			throw error;
		}
	}

	/**
	 * Process next batch of webhooks from queue
	 *
	 * Retrieves and processes webhooks in priority order with proper
	 * status tracking and timeout handling. Implements batch processing
	 * for efficiency within runtime constraints.
	 *
	 * @param options - Processing options including batch size
	 * @returns Batch processing results
	 */
	async processBatch(
		options: QueueProcessingOptions = {},
	): Promise<BatchProcessingResult> {
		const { batchSize = 3, maxProcessingTimeMs = 29000 } = options;
		const startTime = Date.now();

		try {
			logDebug("Starting batch processing", { batchSize, maxProcessingTimeMs });

			// Get next batch of webhooks to process
			const webhooks = await this.getNextBatch(batchSize);
			if (webhooks.length === 0) {
				logDebug("No webhooks to process");
				return {
					success: true,
					processedCount: 0,
					batchResults: [],
					retryCount: 0,
					alertsCreated: 0,
				};
			}

			// Mark webhooks as processing
			await this.markAsProcessing(webhooks.map((w) => w.id));

			const results: WebhookProcessingResult[] = [];
			let retryCount = 0;
			let alertsCreated = 0;

			// Process each webhook
			for (const webhook of webhooks) {
				const processingStartTime = Date.now();

				// Check if we're approaching timeout
				if (Date.now() - startTime > maxProcessingTimeMs - 5000) {
					logInfo("Approaching timeout, stopping batch processing");
					break;
				}

				try {
					// Process the webhook (this would call the actual webhook processor)
					const result = await this.processWebhook(webhook);
					results.push(result);

					if (webhook.retryCount > 0) {
						retryCount++;
					}
				} catch (error) {
					const processingTime = Date.now() - processingStartTime;
					const errorMessage =
						error instanceof Error ? error.message : String(error);

					logError("Webhook processing failed", {
						webhookId: webhook.id,
						error: errorMessage,
						processingTime,
					});

					// Handle failure
					const failureResult = await this.handleProcessingFailure(
						webhook,
						errorMessage,
						processingTime,
					);
					results.push(failureResult);

					if (failureResult.status === "failed") {
						// Create alert for processing error
						await this.createAlert({
							alertType: "processing_error",
							severity: "high",
							title: "Webhook Processing Failed",
							description: `Webhook ${webhook.id} failed processing: ${errorMessage}`,
							webhookId: webhook.id,
							patientId: webhook.patientId,
							appointmentId: webhook.appointmentId,
							context: {
								source: webhook.source,
								entityType: webhook.entityType,
								entityId: webhook.entityId,
								retryCount: webhook.retryCount,
								processingTime,
							},
						});
						alertsCreated++;
					}
				}
			}

			logInfo("Batch processing completed", {
				processedCount: results.length,
				retryCount,
				alertsCreated,
				totalTime: Date.now() - startTime,
			});

			return {
				success: true,
				processedCount: results.length,
				batchResults: results,
				retryCount,
				alertsCreated,
			};
		} catch (error) {
			logError("Batch processing failed", error);
			return {
				success: false,
				processedCount: 0,
				batchResults: [],
				retryCount: 0,
				alertsCreated: 0,
			};
		}
	}

	/**
	 * Detect and handle timed-out webhooks
	 *
	 * Identifies webhooks that have exceeded the 29-second processing
	 * timeout based on their started_at timestamp. Moves them to logs
	 * and creates appropriate alerts.
	 *
	 * @returns Timeout detection results
	 */
	async detectTimeouts(): Promise<TimeoutDetectionResult> {
		try {
			const timeoutThreshold = new Date(Date.now() - 29000); // 29 seconds ago

			// Find webhooks that are processing and started before threshold
			const dbTimedOutWebhooks = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.status, "processing"),
						lt(dbSchema.webhookQueue.startedAt, timeoutThreshold),
					),
				);

			if (dbTimedOutWebhooks.length === 0) {
				return { timedOutWebhooks: [], alertsCreated: 0 };
			}

			// Convert database results to typed interfaces
			const timedOutWebhooks = dbTimedOutWebhooks.map(convertDbWebhookToTyped);

			logInfo("Detected timed-out webhooks", {
				count: timedOutWebhooks.length,
			});

			let alertsCreated = 0;

			// Handle each timed-out webhook
			for (const webhook of timedOutWebhooks) {
				const processingTime = webhook.startedAt
					? Date.now() - webhook.startedAt.getTime()
					: undefined;

				// Move to logs with timeout status
				await this.moveToLogs({
					...webhook,
					status: "timeout",
					completedAt: new Date(),
					processingTimeMs: processingTime,
				});

				// Create timeout alert
				await this.createAlert({
					alertType: "webhook_timeout",
					severity: "high",
					title: "Webhook Processing Timeout",
					description: `Webhook ${webhook.id} exceeded 29-second processing limit`,
					webhookId: webhook.id,
					patientId: webhook.patientId,
					appointmentId: webhook.appointmentId,
					context: {
						source: webhook.source,
						entityType: webhook.entityType,
						entityId: webhook.entityId,
						processingTime,
						startedAt: webhook.startedAt,
					},
				});
				alertsCreated++;
			}

			// Remove from queue
			await this.db.delete(dbSchema.webhookQueue).where(
				inArray(
					dbSchema.webhookQueue.id,
					timedOutWebhooks.map((w) => w.id),
				),
			);

			logInfo("Handled timed-out webhooks", {
				count: timedOutWebhooks.length,
				alertsCreated,
			});

			return { timedOutWebhooks, alertsCreated };
		} catch (error) {
			logError("Failed to detect timeouts", error);
			return { timedOutWebhooks: [], alertsCreated: 0 };
		}
	}

	/**
	 * Get queue statistics
	 *
	 * @returns Current queue statistics
	 */
	async getQueueStatistics(): Promise<QueueStatistics> {
		try {
			const stats = await this.db
				.select({
					status: dbSchema.webhookQueue.status,
					createdAt: dbSchema.webhookQueue.createdAt,
				})
				.from(dbSchema.webhookQueue);

			const statusCounts = stats.reduce(
				(acc, item) => {
					acc[item.status] = (acc[item.status] || 0) + 1;
					return acc;
				},
				{} as Record<string, number>,
			);

			const pendingItems = stats.filter((s) => s.status === "pending");
			const oldestPendingAge =
				pendingItems.length > 0
					? Math.min(
							...pendingItems.map((p) => Date.now() - p.createdAt.getTime()),
						)
					: undefined;

			return {
				pending: statusCounts.pending || 0,
				processing: statusCounts.processing || 0,
				completed: statusCounts.completed || 0,
				failed: statusCounts.failed || 0,
				timeout: statusCounts.timeout || 0,
				duplicateSkipped: statusCounts.duplicate_skipped || 0,
				totalInQueue: stats.length,
				oldestPendingAge,
			};
		} catch (error) {
			logError("Failed to get queue statistics", error);
			throw error;
		}
	}

	/**
	 * Check for duplicate webhooks with enhanced API-triggered detection
	 *
	 * Performs comprehensive duplicate detection with the following priority:
	 * 1. API-triggered duplicates (highest confidence)
	 * 2. Exact duplicates (same source, entity type, entity ID)
	 * 3. Cross-platform duplicates via patient relationship
	 * 4. Appointment-based duplicates
	 *
	 * @param webhook - Webhook to check for duplicates
	 * @returns Enhanced duplicate detection result
	 */
	private async checkForDuplicates(
		webhook: WebhookQueueInsert,
	): Promise<DuplicateDetectionResult> {
		try {
			// Step 1: Check for API-triggered duplicates (highest priority)
			const apiTriggeredResult =
				await this.lockManager.checkForApiTriggeredDuplicate({
					source: webhook.source,
					entityType: webhook.entityType,
					patientId: webhook.patientId,
					appointmentId: webhook.appointmentId,
				});

			if (apiTriggeredResult.isDuplicate) {
				return {
					isDuplicate: true,
					originalWebhookId: apiTriggeredResult.originalWebhookId,
					reason:
						apiTriggeredResult.reason || "API-triggered duplicate detected",
					apiTriggered: apiTriggeredResult,
					confidence: "high",
				};
			}

			// Step 2: Check for exact duplicates (same source, entity type, entity ID)
			const exactDuplicate = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.source, webhook.source),
						eq(dbSchema.webhookQueue.entityType, webhook.entityType),
						eq(dbSchema.webhookQueue.entityId, webhook.entityId),
						inArray(dbSchema.webhookQueue.status, ["pending", "processing"]),
					),
				)
				.limit(1);

			if (exactDuplicate.length > 0) {
				return {
					isDuplicate: true,
					originalWebhookId: exactDuplicate[0].id,
					reason: "Exact duplicate: same source, entity type, and entity ID",
					confidence: "high",
				};
			}

			// Step 3: Check for cross-platform duplicates via patient relationship
			if (webhook.patientId) {
				const crossPlatformDuplicate = await this.db
					.select()
					.from(dbSchema.webhookQueue)
					.where(
						and(
							eq(dbSchema.webhookQueue.patientId, webhook.patientId),
							eq(dbSchema.webhookQueue.entityType, webhook.entityType),
							inArray(dbSchema.webhookQueue.status, ["pending", "processing"]),
							// Different source platform
							webhook.source === "cc"
								? eq(dbSchema.webhookQueue.source, "ap")
								: eq(dbSchema.webhookQueue.source, "cc"),
						),
					)
					.limit(1);

				if (crossPlatformDuplicate.length > 0) {
					return {
						isDuplicate: true,
						originalWebhookId: crossPlatformDuplicate[0].id,
						reason: "Cross-platform duplicate: same patient and entity type",
						lockType: "patient_sync",
						confidence: "medium",
					};
				}
			}

			// Step 4: Check for appointment-based duplicates
			if (webhook.appointmentId) {
				const appointmentDuplicate = await this.db
					.select()
					.from(dbSchema.webhookQueue)
					.where(
						and(
							eq(dbSchema.webhookQueue.appointmentId, webhook.appointmentId),
							eq(dbSchema.webhookQueue.entityType, webhook.entityType),
							inArray(dbSchema.webhookQueue.status, ["pending", "processing"]),
						),
					)
					.limit(1);

				if (appointmentDuplicate.length > 0) {
					return {
						isDuplicate: true,
						originalWebhookId: appointmentDuplicate[0].id,
						reason: "Appointment duplicate: same appointment and entity type",
						lockType: "appointment_sync",
						confidence: "medium",
					};
				}
			}

			return { isDuplicate: false };
		} catch (error) {
			logError("Failed to check for duplicates", error);
			// On error, assume not duplicate to avoid blocking processing
			return { isDuplicate: false };
		}
	}

	/**
	 * Insert webhook into queue
	 *
	 * @param webhook - Webhook data to insert
	 * @returns Webhook ID
	 */
	private async insertWebhook(webhook: WebhookQueueInsert): Promise<string> {
		const webhookId = crypto.randomUUID();

		await this.db.insert(dbSchema.webhookQueue).values({
			id: webhookId,
			status: "pending",
			priority: webhook.priority || 100,
			source: webhook.source,
			entityType: webhook.entityType,
			entityId: webhook.entityId,
			patientId: webhook.patientId,
			appointmentId: webhook.appointmentId,
			payload: webhook.payload,
			retryCount: 0,
			maxRetries: webhook.maxRetries || 3,
		});

		return webhookId;
	}

	/**
	 * Get next batch of webhooks to process
	 *
	 * @param batchSize - Number of webhooks to retrieve
	 * @returns Array of webhook items
	 */
	private async getNextBatch(batchSize: number): Promise<WebhookQueueItem[]> {
		// Get pending webhooks ordered by priority (desc) and creation time (asc)
		// Also include failed webhooks that are ready for retry
		const now = new Date();

		const dbWebhooks = await this.db
			.select()
			.from(dbSchema.webhookQueue)
			.where(
				or(
					eq(dbSchema.webhookQueue.status, "pending"),
					and(
						eq(dbSchema.webhookQueue.status, "failed"),
						or(
							isNull(dbSchema.webhookQueue.nextRetryAt),
							lte(dbSchema.webhookQueue.nextRetryAt, now),
						),
					),
				),
			)
			.orderBy(
				dbSchema.webhookQueue.priority, // DESC by default for higher priority first
				dbSchema.webhookQueue.createdAt, // ASC for FIFO within same priority
			)
			.limit(batchSize);

		// Convert database results to typed interfaces
		return dbWebhooks.map(convertDbWebhookToTyped);
	}

	/**
	 * Mark webhooks as processing
	 *
	 * @param webhookIds - Array of webhook IDs to mark as processing
	 */
	private async markAsProcessing(webhookIds: string[]): Promise<void> {
		await this.db
			.update(dbSchema.webhookQueue)
			.set({
				status: "processing",
				startedAt: new Date(),
				updatedAt: new Date(),
			})
			.where(inArray(dbSchema.webhookQueue.id, webhookIds));
	}

	/**
	 * Process individual webhook
	 *
	 * Routes webhook to appropriate processor based on source platform
	 * and handles lock creation/cleanup for API-triggered duplicate prevention.
	 *
	 * @param webhook - Webhook to process
	 * @returns Processing result
	 */
	private async processWebhook(
		webhook: WebhookQueueItem,
	): Promise<WebhookProcessingResult> {
		const startTime = Date.now();

		try {
			logDebug("Processing webhook", {
				webhookId: webhook.id,
				source: webhook.source,
				entityType: webhook.entityType,
				entityId: webhook.entityId,
			});

			let processingResult: { success: boolean; error?: string } = {
				success: false,
			};

			// Route to appropriate processor based on source
			if (webhook.source === "cc") {
				processingResult = await this.processCCWebhook(webhook);
			} else if (webhook.source === "ap") {
				processingResult = await this.processAPWebhook(webhook);
			} else {
				// This should never happen due to discriminated union types
				const exhaustiveCheck: never = webhook;
				throw new Error(
					`Unsupported webhook source: ${JSON.stringify(exhaustiveCheck)}`,
				);
			}

			const processingTime = Date.now() - startTime;

			if (processingResult.success) {
				// Clean up any duplicate prevention locks created by this webhook
				await this.removeDuplicatePreventionLock(webhook.id);

				// Mark as completed and move to logs
				await this.moveToLogs({
					...webhook,
					status: "completed",
					completedAt: new Date(),
					processingTimeMs: processingTime,
				});

				// Remove from queue
				await this.db
					.delete(dbSchema.webhookQueue)
					.where(eq(dbSchema.webhookQueue.id, webhook.id));

				return {
					webhookId: webhook.id,
					status: "completed",
					processingTimeMs: processingTime,
				};
			} else {
				throw new Error(processingResult.error || "Webhook processing failed");
			}
		} catch (error) {
			const processingTime = Date.now() - startTime;
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			throw new Error(
				`Processing failed after ${processingTime}ms: ${errorMessage}`,
			);
		}
	}

	/**
	 * Handle processing failure
	 *
	 * @param webhook - Failed webhook
	 * @param errorMessage - Error message
	 * @param processingTime - Processing time in milliseconds
	 * @returns Processing result
	 */
	private async handleProcessingFailure(
		webhook: WebhookQueueItem,
		errorMessage: string,
		processingTime: number,
	): Promise<WebhookProcessingResult> {
		const newRetryCount = webhook.retryCount + 1;

		if (newRetryCount >= webhook.maxRetries) {
			// Max retries exceeded, move to logs as failed
			await this.moveToLogs({
				...webhook,
				status: "failed",
				completedAt: new Date(),
				retryCount: newRetryCount,
				errorMessage,
				processingTimeMs: processingTime,
			});

			// Remove from queue
			await this.db
				.delete(dbSchema.webhookQueue)
				.where(eq(dbSchema.webhookQueue.id, webhook.id));

			return {
				webhookId: webhook.id,
				status: "failed",
				processingTimeMs: processingTime,
				errorMessage,
			};
		} else {
			// Schedule retry with exponential backoff
			const retryDelayMs = Math.min(1000 * 2 ** newRetryCount, 300000); // Max 5 minutes
			const nextRetryAt = new Date(Date.now() + retryDelayMs);

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "failed",
					retryCount: newRetryCount,
					nextRetryAt,
					errorMessage,
					processingTimeMs: processingTime,
					priority: 50, // Lower priority for retries
					updatedAt: new Date(),
				})
				.where(eq(dbSchema.webhookQueue.id, webhook.id));

			return {
				webhookId: webhook.id,
				status: "failed",
				processingTimeMs: processingTime,
				errorMessage,
			};
		}
	}

	/**
	 * Move webhook to logs table
	 *
	 * @param webhook - Webhook data to move to logs
	 */
	private async moveToLogs(
		webhook:
			| (Omit<WebhookQueueLogItem, "id"> & { id?: string })
			| (Omit<WebhookQueueItem, "id"> & {
					id?: string;
					status: WebhookQueueLogStatus;
			  }),
	): Promise<void> {
		const logId = webhook.id || crypto.randomUUID();

		await this.db.insert(dbSchema.webhookQueueLogs).values({
			id: logId,
			createdAt: webhook.createdAt,
			updatedAt: new Date(),
			status: webhook.status,
			priority: webhook.priority,
			source: webhook.source,
			entityType: webhook.entityType,
			entityId: webhook.entityId,
			patientId: webhook.patientId || null,
			appointmentId: webhook.appointmentId || null,
			payload: webhook.payload,
			startedAt: webhook.startedAt || null,
			completedAt: webhook.completedAt || null,
			retryCount: webhook.retryCount,
			errorMessage: webhook.errorMessage || null,
			processingTimeMs: webhook.processingTimeMs || null,
			duplicateOf: webhook.duplicateOf || null,
			duplicateReason: webhook.duplicateReason || null,
		});
	}

	/**
	 * Create alert
	 *
	 * @param alert - Alert data
	 */
	private async createAlert(alert: {
		alertType: AlertType;
		severity: AlertSeverity;
		title: string;
		description: string;
		webhookId?: string;
		patientId?: string;
		appointmentId?: string;
		context?: Record<string, unknown>;
	}): Promise<void> {
		await this.db.insert(dbSchema.alerts).values({
			id: crypto.randomUUID(),
			alertType: alert.alertType,
			severity: alert.severity,
			title: alert.title,
			description: alert.description,
			webhookId: alert.webhookId,
			patientId: alert.patientId,
			appointmentId: alert.appointmentId,
			context: alert.context,
			resolved: 0,
		});
	}

	/**
	 * Clean up expired duplicate prevention locks
	 */
	async cleanupExpiredLocks(): Promise<number> {
		return await this.lockManager.cleanupExpiredLocks();
	}

	/**
	 * Move completed/failed webhooks from queue to logs
	 * This is a maintenance operation to keep the queue table clean
	 */
	async archiveCompletedWebhooks(): Promise<number> {
		try {
			// Get completed/failed webhooks that are still in the queue
			const dbCompletedWebhooks = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					inArray(dbSchema.webhookQueue.status, [
						"completed",
						"failed",
						"timeout",
					]),
				);

			if (dbCompletedWebhooks.length === 0) {
				return 0;
			}

			// Convert to typed interfaces and move to logs
			for (const dbWebhook of dbCompletedWebhooks) {
				const typedWebhook = convertDbWebhookToTyped(dbWebhook);
				// Cast status to log status since we're only archiving completed/failed/timeout webhooks
				await this.moveToLogs({
					...typedWebhook,
					status: typedWebhook.status as WebhookQueueLogStatus,
				});
			}

			// Remove from queue
			await this.db.delete(dbSchema.webhookQueue).where(
				inArray(
					dbSchema.webhookQueue.id,
					dbCompletedWebhooks.map((w) => w.id),
				),
			);

			logInfo("Archived completed webhooks", {
				count: dbCompletedWebhooks.length,
			});
			return dbCompletedWebhooks.length;
		} catch (error) {
			logError("Failed to archive completed webhooks", error);
			return 0;
		}
	}

	/**
	 * Create duplicate prevention lock
	 *
	 * Creates a lock before making cross-platform API calls to prevent
	 * subsequent webhooks from the target platform from being processed
	 * as duplicates.
	 *
	 * @param webhookId - ID of webhook making the API call
	 * @param targetPlatform - Platform that will receive the API call
	 * @param patientId - Patient ID for patient-related locks
	 * @param appointmentId - Appointment ID for appointment-related locks
	 * @param sourceEntityId - Source platform entity ID (fallback for new patients)
	 */
	async createDuplicatePreventionLock(
		webhookId: string,
		targetPlatform: "ap" | "cc",
		patientId?: string,
		appointmentId?: string,
		sourceEntityId?: string,
	): Promise<void> {
		await this.lockManager.createLock({
			webhookId,
			targetPlatform,
			patientId,
			appointmentId,
			sourceEntityId,
		});
	}

	/**
	 * Remove duplicate prevention lock
	 *
	 * Removes the lock when webhook processing completes.
	 *
	 * @param webhookId - ID of the webhook that created the lock
	 */
	async removeDuplicatePreventionLock(webhookId: string): Promise<void> {
		await this.lockManager.removeLock(webhookId);
	}

	/**
	 * Process CC webhook with lock integration
	 *
	 * @param webhook - CC webhook to process
	 * @returns Processing result
	 */
	private async processCCWebhook(
		webhook: WebhookQueueItem,
	): Promise<{ success: boolean; error?: string }> {
		try {
			// Import type guards and CC webhook processor dynamically to avoid circular imports
			const { isCCWebhookQueueItem } = await import("./types");
			const { processWebhookEvent } = await import("@/processors/ccWebhook");

			// Type-safe validation using type guard
			if (!isCCWebhookQueueItem(webhook)) {
				throw new Error(
					`Invalid webhook source for CC processing: expected 'cc', got '${webhook.source}'`,
				);
			}

			// Now TypeScript knows webhook is CCWebhookQueueItem and payload is CCWebhookPayload
			const ccPayload = webhook.payload;
			if (!ccPayload.event || !ccPayload.model) {
				throw new Error("Invalid CC webhook payload structure");
			}

			// Resolve patient ID during processing (deferred from queue insertion)
			let patientId: string | undefined;
			if (ccPayload.model === "Patient") {
				// For patient webhooks, the entity ID is the patient ID
				patientId = await this.resolvePatientId("cc", webhook.entityId);
			} else if (ccPayload.model === "Appointment") {
				// For appointment webhooks, extract patient ID from payload
				const appointmentPayload = ccPayload.payload as Record<string, unknown>;
				const patients = appointmentPayload?.patients as number[] | undefined;
				if (patients?.[0]) {
					patientId = await this.resolvePatientId("cc", patients[0].toString());
				}
			}

			if (patientId) {
				// Update the webhook record with resolved patient ID for duplicate detection
				await this.updateWebhookPatientId(webhook.id, patientId);
			}

			// Create enhanced config with webhook ID and resolved patient ID for lock creation
			const config: Partial<WebhookProcessingConfig> = {
				webhookId: webhook.id,
				queueManager: {
					createDuplicatePreventionLock: async (options) => {
						// Use resolved patient ID if available, otherwise use the provided one
						const lockPatientId = options.patientId || patientId;
						// Use CC entity ID as fallback for new patients
						const sourceEntityId = webhook.entityId;
						await this.createDuplicatePreventionLock(
							options.webhookId,
							options.targetPlatform,
							lockPatientId,
							options.appointmentId,
							sourceEntityId,
						);
					},
				},
			};

			// Process the webhook with properly typed payload
			const result = await processWebhookEvent(ccPayload, config);

			if (result.success) {
				// Trigger custom field synchronization for created/updated patients
				if (
					result.patientSync?.patient &&
					["created", "updated"].includes(result.patientSync.action)
				) {
					const patientId = result.patientSync.patient.id;
					logInfo(
						"Triggering custom field sync after successful patient processing (queue)",
						{
							patientId,
							action: result.patientSync.action,
							webhookId: webhook.id,
						},
					);

					// Fire-and-forget unidirectional custom field sync (CC → AP)
					// CliniCore webhook means CC data changed, so sync to AutoPatient
					fireAndForgetCustomFieldSync(patientId, "ap", {
						webhookId: webhook.id,
						source: "queue_cc_webhook",
						action: result.patientSync.action,
					});
				}

				return { success: true };
			} else {
				return {
					success: false,
					error: result.error?.message || "CC webhook processing failed",
				};
			}
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}

	/**
	 * Process AP webhook with lock integration
	 *
	 * @param webhook - AP webhook to process
	 * @returns Processing result
	 */
	private async processAPWebhook(
		webhook: WebhookQueueItem,
	): Promise<{ success: boolean; error?: string }> {
		try {
			// Import AP webhook processor dynamically to avoid circular imports
			const { processContactWebhookEvent } = await import(
				"@/processors/apWebhook"
			);

			// Validate payload type - use type guard for AP webhook
			if (!isAPWebhookQueueItem(webhook)) {
				throw new Error("Invalid webhook type for AP processing");
			}

			const apPayload = webhook.payload;
			if (!apPayload.contact_id) {
				throw new Error("Invalid AP webhook payload structure");
			}

			// Resolve patient ID during processing (deferred from queue insertion)
			const patientId = await this.resolvePatientId("ap", apPayload.contact_id);
			if (patientId) {
				// Update the webhook record with resolved patient ID for duplicate detection
				await this.updateWebhookPatientId(webhook.id, patientId);
			}

			// Cast to the expected type since we've validated it's an AP contact webhook
			const contactPayload = apPayload as APContactCreationWebhookPayload;

			// Create enhanced config with webhook ID and resolved patient ID for lock creation
			const config: Partial<APWebhookProcessingConfig> = {
				webhookId: webhook.id,
				queueManager: {
					createDuplicatePreventionLock: async (options) => {
						// Use resolved patient ID if available, otherwise use the provided one
						const lockPatientId = options.patientId || patientId;
						// Use AP contact ID as fallback for new patients
						const sourceEntityId = webhook.entityId;
						await this.createDuplicatePreventionLock(
							options.webhookId,
							options.targetPlatform,
							lockPatientId,
							options.appointmentId,
							sourceEntityId,
						);
					},
				},
			};

			// Process the webhook
			const result = await processContactWebhookEvent(contactPayload, config);

			if (result.success) {
				// Trigger custom field synchronization for created/updated contacts
				if (
					result.contactSync?.dbPatient &&
					["created", "updated"].includes(result.contactSync.action)
				) {
					const patientId = result.contactSync.dbPatient.id;
					logInfo(
						"Triggering custom field sync after successful contact processing (queue)",
						{
							patientId,
							action: result.contactSync.action,
							webhookId: webhook.id,
						},
					);

					// Fire-and-forget unidirectional custom field sync (AP → CC)
					// AutoPatient webhook means AP data changed, so sync to CliniCore
					fireAndForgetCustomFieldSync(patientId, "cc", {
						webhookId: webhook.id,
						source: "queue_ap_webhook",
						action: result.contactSync.action,
					});
				}

				return { success: true };
			} else {
				return {
					success: false,
					error: result.error?.message || "AP webhook processing failed",
				};
			}
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}

	/**
	 * Resolve patient ID from source platform entity ID
	 *
	 * Looks up existing patient record based on source platform ID.
	 * This is used during webhook processing to resolve patient IDs
	 * that were deferred from queue insertion time.
	 *
	 * @param source - Source platform ("ap" or "cc")
	 * @param entityId - Entity ID from source platform
	 * @returns Patient ID if found, undefined otherwise
	 */
	private async resolvePatientId(
		source: "ap" | "cc",
		entityId: string,
	): Promise<string | undefined> {
		try {
			if (source === "cc") {
				const result = await this.db
					.select({ id: dbSchema.patient.id })
					.from(dbSchema.patient)
					.where(eq(dbSchema.patient.ccId, parseInt(entityId)))
					.limit(1);

				return result[0]?.id;
			} else {
				const result = await this.db
					.select({ id: dbSchema.patient.id })
					.from(dbSchema.patient)
					.where(eq(dbSchema.patient.apId, entityId))
					.limit(1);

				return result[0]?.id;
			}
		} catch (error) {
			logError("Failed to resolve patient ID", {
				source,
				entityId,
				error: error instanceof Error ? error.message : String(error),
			});
			return undefined;
		}
	}

	/**
	 * Update webhook record with resolved patient ID
	 *
	 * Updates the patient_id field in the webhook queue record after
	 * successful patient ID resolution during processing.
	 *
	 * @param webhookId - Webhook ID to update
	 * @param patientId - Resolved patient ID
	 */
	private async updateWebhookPatientId(
		webhookId: string,
		patientId: string,
	): Promise<void> {
		try {
			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					patientId,
					updatedAt: new Date(),
				})
				.where(eq(dbSchema.webhookQueue.id, webhookId));

			logDebug("Updated webhook with resolved patient ID", {
				webhookId,
				patientId,
			});
		} catch (error) {
			logError("Failed to update webhook patient ID", {
				webhookId,
				patientId,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}
}
